FROM rust:1.88 as builder
WORKDIR /usr/src
RUN cargo new app
WORKDIR /usr/src/app
COPY Cargo.toml Cargo.lock ./
RUN cargo build --release
COPY ./src ./src/
RUN touch ./src/main.rs
RUN cargo build --release
RUN rm -rf ./src

FROM debian:bullseye-slim
RUN apt update && apt install ca-certificates openssl -y
RUN update-ca-certificates
RUN rm -rf /var/lib/apt/lists/*
COPY --from=builder /usr/src/app/target/release/api_server /usr/local/bin/api_server
EXPOSE 3000
CMD ["api_server"]
