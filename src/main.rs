use anyhow::Result;
use clap::Parser;
use config::{Config, Environment};
use std::sync::Arc;
use tracing::{info, warn};
use tracing_subscriber;

mod config_types;
mod gsm;
mod serial;

use crate::config_types::AppConfig;
use crate::gsm::service::GsmService;

#[derive(Parser)]
#[command(name = "sms-worker")]
#[command(about = "A Rust SMS receiver service for GSM modems")]
struct Cli {
    /// Configuration file path
    #[arg(short, long, default_value = "config.toml")]
    config: String,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt()
        .with_env_filter(tracing_subscriber::EnvFilter::from_default_env())
        .init();

    info!("Starting SMS Worker Rust");

    let cli = Cli::parse();

    // Load configuration
    let config = load_config(&cli.config)?;
    info!("Configuration loaded successfully");

    // Initialize Kafka client
    // let kafka_client = Arc::new(KafkaClient::new(&config.kafka).await?);
    // info!("Kafka client initialized");

    // Initialize GSM service
    let gsm_service = GsmService::new(config.clone()).await?;
    info!("GSM service initialized");

    // Start the service
    gsm_service.start().await?;

    info!("SMS Worker Rust started successfully");

    // Keep the application running
    tokio::signal::ctrl_c().await?;
    info!("Received shutdown signal, stopping...");

    Ok(())
}

fn load_config(config_path: &str) -> Result<AppConfig> {
    let mut config_builder = Config::builder()
        .add_source(config::File::with_name(config_path).required(false))
        .add_source(Environment::with_prefix("SMS_WORKER"));

    let config = config_builder.build()?;
    let app_config: AppConfig = config.try_deserialize()?;

    Ok(app_config)
}
