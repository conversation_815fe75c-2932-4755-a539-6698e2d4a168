use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub enum GsmEventType {
    Open,
    Close,
    Error,
    NewMessage,
    SimId,
    AtCommandResponse,
    ModemInfo,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SmsMessage {
    pub sender: String,
    pub content: String,
    pub received_at: DateTime<Utc>,
}

#[derive(<PERSON>bu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct AtCommandResponse {
    pub command: String,
    pub response: String,
    pub success: bool,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModemInfo {
    pub device_path: String,
    pub sim_id: Option<String>,
    pub manufacturer: Option<String>,
    pub model: Option<String>,
    pub revision: Option<String>,
    pub serial_number: Option<String>,
    pub signal_quality: Option<String>,
    pub network_status: Option<String>,
    pub operator: Option<String>,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CreateMessageDto {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sim_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub device_key: Option<String>,
    pub content: String,
    pub received_at: DateTime<Utc>,
    pub sender: String,
}

#[derive(Debug, Clone)]
pub struct DeviceInfo {
    pub path: String,
    pub vendor_id: Option<String>,
    pub product_id: Option<String>,
    pub serial_number: Option<String>,
}

impl DeviceInfo {
    pub fn device_hash(&self) -> String {
        format!(
            "{}_{}_{}", 
            self.product_id.as_deref().unwrap_or("unknown"),
            self.vendor_id.as_deref().unwrap_or("unknown"),
            self.serial_number.as_deref().unwrap_or(&self.path)
        )
    }
}

#[derive(Debug, Clone)]
pub struct GsmModemInfo {
    pub device_key: String,
    pub device_path: String,
    pub sim_id: Option<String>,
}

// GSM modem vendor/product ID filters (from original TypeScript code)
pub const SUPPORTED_MODEMS: &[(Option<&str>, Option<&str>)] = &[
    (Some("1a86"), Some("7523")), // CH340 USB-to-serial converter
    (Some("2c7c"), Some("0125")), // Quectel EG25-G
];

pub fn is_supported_modem(vendor_id: Option<&str>, product_id: Option<&str>) -> bool {
    SUPPORTED_MODEMS.iter().any(|(v, p)| {
        vendor_id == *v && product_id == *p
    })
}
