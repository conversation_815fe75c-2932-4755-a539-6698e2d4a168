use anyhow::{anyhow, Result};
use chrono::Utc;
use regex::Regex;
use std::time::Duration;
use tokio::io::{AsyncBufReadExt, AsyncWriteExt, BufReader};
use tokio::time::timeout;
use tokio_serial::{SerialPortBuilderExt, SerialStream};
use tracing::{debug, error, info, warn};

use super::types::{AtCommandResponse, ModemInfo};

/// AT Command utility for direct GSM modem communication
pub struct AtCommander {
    device_path: String,
    baud_rate: u32,
}

impl AtCommander {
    pub fn new(device_path: String, baud_rate: u32) -> Self {
        Self {
            device_path,
            baud_rate,
        }
    }

    /// Connect to the modem and return a port handle
    pub async fn connect(&self) -> Result<SerialStream> {
        info!("🔌 Connecting to GSM modem at {} ({})", self.device_path, self.baud_rate);
        
        let port = tokio_serial::new(&self.device_path, self.baud_rate)
            .data_bits(tokio_serial::DataBits::Eight)
            .stop_bits(tokio_serial::StopBits::One)
            .parity(tokio_serial::Parity::None)
            .flow_control(tokio_serial::FlowControl::None)
            .timeout(Duration::from_millis(1000))
            .open_native_async()
            .map_err(|e| anyhow!("Failed to open serial port {}: {}", self.device_path, e))?;

        info!("✅ Connected to GSM modem successfully");
        Ok(port)
    }

    /// Send a single AT command and get response
    pub async fn send_command(&self, port: &mut SerialStream, command: &str) -> Result<AtCommandResponse> {
        let start_time = Utc::now();
        info!("📤 Sending AT command: {}", command);

        let cmd_with_cr = format!("{}\r\n", command);
        port.write_all(cmd_with_cr.as_bytes()).await
            .map_err(|e| anyhow!("Failed to write command: {}", e))?;
        
        // Read response with timeout
        let mut reader = BufReader::new(port);
        let mut response = String::new();
        
        let result = timeout(Duration::from_secs(10), reader.read_line(&mut response)).await;
        
        let (response_text, success) = match result {
            Ok(Ok(_)) => {
                let trimmed = response.trim().to_string();
                info!("📥 AT command '{}' response: {}", command, trimmed);
                let success = !trimmed.contains("ERROR") && !trimmed.is_empty();
                (trimmed, success)
            }
            Ok(Err(e)) => {
                let error_msg = format!("Failed to read response: {}", e);
                error!("❌ {}", error_msg);
                (error_msg, false)
            }
            Err(_) => {
                let error_msg = "Command timeout".to_string();
                error!("⏰ AT command '{}' timed out", command);
                (error_msg, false)
            }
        };

        Ok(AtCommandResponse {
            command: command.to_string(),
            response: response_text,
            success,
            timestamp: start_time,
        })
    }

    /// Get comprehensive SIM card information
    pub async fn get_sim_info(&self, port: &mut SerialStream) -> Result<Vec<AtCommandResponse>> {
        info!("📱 Retrieving SIM card information...");

        let sim_commands = vec![
            ("AT+CIMI", "SIM IMSI"),
            ("AT+CCID", "SIM ICCID"),
            ("AT+CPIN?", "PIN status"),
            ("AT+CNUM", "Own phone number"),
            ("AT+CMGF=0", "Set SMS PDU mode"),
        ];

        let mut responses = Vec::new();
        
        for (cmd, description) in sim_commands {
            info!("🔍 Getting {}: {}", description, cmd);
            match self.send_command(port, cmd).await {
                Ok(response) => {
                    responses.push(response);
                }
                Err(e) => {
                    warn!("⚠️  Failed to get {}: {}", description, e);
                    responses.push(AtCommandResponse {
                        command: cmd.to_string(),
                        response: format!("Error: {}", e),
                        success: false,
                        timestamp: Utc::now(),
                    });
                }
            }
            tokio::time::sleep(Duration::from_millis(500)).await;
        }

        Ok(responses)
    }

    /// Get comprehensive modem information
    pub async fn get_modem_info(&self, port: &mut SerialStream) -> Result<ModemInfo> {
        info!("📡 Retrieving modem information...");
        
        let mut modem_info = ModemInfo {
            device_path: self.device_path.clone(),
            sim_id: None,
            manufacturer: None,
            model: None,
            revision: None,
            serial_number: None,
            signal_quality: None,
            network_status: None,
            operator: None,
            timestamp: Utc::now(),
        };

        // Get SIM ID
        if let Ok(response) = self.send_command(port, "AT+CCID").await {
            if response.success {
                modem_info.sim_id = self.extract_sim_id(&response.response);
            }
        }

        // Get manufacturer
        if let Ok(response) = self.send_command(port, "AT+CGMI").await {
            if response.success {
                modem_info.manufacturer = Some(response.response.clone());
            }
        }

        // Get model
        if let Ok(response) = self.send_command(port, "AT+CGMM").await {
            if response.success {
                modem_info.model = Some(response.response.clone());
            }
        }

        // Get revision
        if let Ok(response) = self.send_command(port, "AT+CGMR").await {
            if response.success {
                modem_info.revision = Some(response.response.clone());
            }
        }

        // Get serial number
        if let Ok(response) = self.send_command(port, "AT+CGSN").await {
            if response.success {
                modem_info.serial_number = Some(response.response.clone());
            }
        }

        // Get signal quality
        if let Ok(response) = self.send_command(port, "AT+CSQ").await {
            if response.success {
                modem_info.signal_quality = Some(response.response.clone());
            }
        }

        // Get network registration status
        if let Ok(response) = self.send_command(port, "AT+CREG?").await {
            if response.success {
                modem_info.network_status = Some(response.response.clone());
            }
        }

        // Get current operator
        if let Ok(response) = self.send_command(port, "AT+COPS?").await {
            if response.success {
                modem_info.operator = Some(response.response.clone());
            }
        }

        Ok(modem_info)
    }

    /// Extract SIM ID from AT command response
    fn extract_sim_id(&self, response: &str) -> Option<String> {
        let patterns = vec![
            r"89\d{17,18}",
            r#""(89\d{17,18})""#,
            r"ICCID:\s*(89\d{17,18})",
            r"\+CCID:\s*(89\d{17,18})",
        ];

        for pattern in patterns {
            let regex = Regex::new(pattern).unwrap();
            if let Some(captures) = regex.captures(response) {
                let sim_id = if captures.len() > 1 {
                    captures.get(1).unwrap().as_str()
                } else {
                    captures.get(0).unwrap().as_str()
                };
                
                if sim_id.starts_with("89") && sim_id.len() >= 19 && sim_id.len() <= 20 {
                    return Some(sim_id.to_string());
                }
            }
        }
        None
    }

    /// Test basic connectivity
    pub async fn test_connectivity(&self, port: &mut SerialStream) -> Result<bool> {
        info!("🔍 Testing modem connectivity...");
        
        let response = self.send_command(port, "AT").await?;
        let connected = response.success && (response.response.contains("OK") || response.response.is_empty());
        
        if connected {
            info!("✅ Modem connectivity test passed");
        } else {
            warn!("❌ Modem connectivity test failed: {}", response.response);
        }
        
        Ok(connected)
    }

    /// Send multiple commands in sequence
    pub async fn send_commands(&self, port: &mut SerialStream, commands: Vec<&str>) -> Result<Vec<AtCommandResponse>> {
        let mut responses = Vec::new();
        
        for command in commands {
            match self.send_command(port, command).await {
                Ok(response) => responses.push(response),
                Err(e) => {
                    responses.push(AtCommandResponse {
                        command: command.to_string(),
                        response: format!("Error: {}", e),
                        success: false,
                        timestamp: Utc::now(),
                    });
                }
            }
            tokio::time::sleep(Duration::from_millis(300)).await;
        }
        
        Ok(responses)
    }
}
