use anyhow::{anyhow, Result};
use chrono::{DateTime, Utc};
use regex::Regex;
use std::sync::Arc;
use std::time::Duration;
use tokio::io::{AsyncBufReadExt, AsyncWriteExt, BufReader};
use tokio::sync::mpsc;
use tokio::time::{sleep, timeout};
use tokio_serial::{SerialPort, SerialPortBuilderExt, SerialStream};
use tracing::{debug, error, info, warn};

use super::types::{GsmEventType, SmsMessage};

pub struct GsmEntity {
    device_path: String,
    baud_rate: u32,
    timeout_duration: Duration,
    heartbeat_interval: Duration,
    pub sim_id: Option<String>,
    event_sender: mpsc::UnboundedSender<(GsmEventType, Option<Vec<SmsMessage>>)>,
}

impl GsmEntity {
    pub fn new(
        device_path: String,
        baud_rate: u32,
        timeout_seconds: u64,
        heartbeat_interval_seconds: u64,
        event_sender: mpsc::UnboundedSender<(GsmEventType, Option<Vec<SmsMessage>>)>,
    ) -> Self {
        Self {
            device_path,
            baud_rate,
            timeout_duration: Duration::from_secs(timeout_seconds),
            heartbeat_interval: Duration::from_secs(heartbeat_interval_seconds),
            sim_id: None,
            event_sender,
        }
    }

    /// Get the current SIM ID if available
    pub fn get_current_sim_id(&self) -> Option<&String> {
        self.sim_id.as_ref()
    }

    /// Get device path
    pub fn get_device_path(&self) -> &str {
        &self.device_path
    }

    /// Send SMS using PDU format
    pub async fn send_sms_pdu(&self, phone_number: &str, message: &str) -> Result<String> {
        info!("📤 Preparing to send SMS to {} via PDU", phone_number);

        // Generate PDU data
        let pdu_data = self.create_pdu(phone_number, message)?;
        let pdu_length = self.calculate_pdu_length(&pdu_data)?;

        info!("📱 Generated PDU (length: {}): {}", pdu_length, pdu_data);

        // Create a temporary connection for sending SMS
        let mut port = tokio_serial::new(&self.device_path, self.baud_rate)
            .data_bits(tokio_serial::DataBits::Eight)
            .stop_bits(tokio_serial::StopBits::One)
            .parity(tokio_serial::Parity::None)
            .flow_control(tokio_serial::FlowControl::None)
            .timeout(Duration::from_millis(1000))
            .open_native_async()
            .map_err(|e| anyhow!("Failed to open serial port for SMS: {}", e))?;

        // Send AT+CMGS command with PDU length
        let cmgs_command = format!("AT+CMGS={}", pdu_length);
        info!("📤 Sending CMGS command: {}", cmgs_command);

        port.write_all(format!("{}\r\n", cmgs_command).as_bytes()).await?;
        port.flush().await?;

        // Wait for '>' prompt
        let mut response = String::new();
        {
            let mut reader = BufReader::new(&mut port);
            match timeout(Duration::from_secs(5), reader.read_line(&mut response)).await {
                Ok(Ok(_)) => {
                    let trimmed = response.trim();
                    info!("📥 CMGS response: '{}'", trimmed);

                    if !trimmed.contains(">") && !trimmed.is_empty() {
                        return Err(anyhow!("Expected '>' prompt, got: {}", trimmed));
                    }
                }
                Ok(Err(e)) => return Err(anyhow!("Failed to read CMGS response: {}", e)),
                Err(_) => return Err(anyhow!("CMGS command timeout")),
            }
        }

        // Send PDU data followed by Ctrl+Z (0x1A)
        info!("📤 Sending PDU data: {}", pdu_data);
        port.write_all(pdu_data.as_bytes()).await?;
        port.write_all(&[0x1A]).await?; // Ctrl+Z to end PDU
        port.flush().await?;

        // Read final response
        response.clear();
        {
            let mut reader = BufReader::new(&mut port);
            match timeout(Duration::from_secs(30), reader.read_line(&mut response)).await {
            Ok(Ok(_)) => {
                let trimmed = response.trim();
                info!("📥 SMS send response: '{}'", trimmed);

                if trimmed.contains("OK") || trimmed.contains("+CMGS:") {
                    info!("✅ SMS sent successfully to {}", phone_number);
                    Ok(trimmed.to_string())
                } else if trimmed.contains("ERROR") {
                    Err(anyhow!("SMS send failed: {}", trimmed))
                } else {
                    Err(anyhow!("Unexpected SMS response: {}", trimmed))
                }
            }
                Ok(Err(e)) => Err(anyhow!("Failed to read SMS response: {}", e)),
                Err(_) => Err(anyhow!("SMS send timeout")),
            }
        }
    }

    pub async fn start(&mut self) -> Result<()> {
        info!("🚀 Starting GSM entity for device: {}", self.device_path);
        let mut retry_count = 0;
        const MAX_RETRIES: u32 = 10;
        const INITIAL_RETRY_DELAY: u64 = 2;

        loop {
            match self.connect_and_run().await {
                Ok(_) => {
                    info!("✅ GSM connection closed normally for {}", self.device_path);
                    break;
                }
                Err(e) => {
                    retry_count += 1;
                    error!("❌ GSM connection error for {} (attempt {}/{}): {}",
                           self.device_path, retry_count, MAX_RETRIES, e);

                    let _ = self.event_sender.send((GsmEventType::Error, None));

                    if retry_count >= MAX_RETRIES {
                        error!("🛑 Max retries reached for device: {}", self.device_path);
                        break;
                    }

                    // Exponential backoff with jitter (2s, 4s, 8s, max 30s)
                    let delay = std::cmp::min(INITIAL_RETRY_DELAY * 2_u64.pow(retry_count - 1), 30);
                    info!("⏳ Retrying connection to {} in {} seconds...", self.device_path, delay);
                    sleep(Duration::from_secs(delay)).await;
                }
            }
        }

        Ok(())
    }

    async fn connect_and_run(&mut self) -> Result<()> {
        info!("🔌 Attempting to open serial port: {} at {} baud", self.device_path, self.baud_rate);

        // Open serial port with timeout
        let mut port = tokio_serial::new(&self.device_path, self.baud_rate)
            .data_bits(tokio_serial::DataBits::Eight)
            .stop_bits(tokio_serial::StopBits::One)
            .parity(tokio_serial::Parity::None)
            .flow_control(tokio_serial::FlowControl::None)
            .timeout(Duration::from_millis(1000))
            .open_native_async()
            .map_err(|e| anyhow!("Failed to open serial port {}: {}", self.device_path, e))?;

        info!("✅ Serial port opened successfully: {}", self.device_path);
        let _ = self.event_sender.send((GsmEventType::Open, None));

        info!("📡 Starting message reading loop with initialization and heartbeat for: {}", self.device_path);
        // Start message reading loop with initialization and periodic AT commands
        self.read_messages_loop_with_init(&mut port).await?;

        Ok(())
    }

    async fn initialize_modem(&mut self, port: &mut SerialStream) -> Result<()> {
        info!("🔧 Initializing GSM modem: {}", self.device_path);

        // Send basic AT commands to initialize the modem
        let init_commands = vec![
            ("AT", "Basic connectivity test"),
            ("ATE0", "Turn off echo"),
            ("AT+CMGF=1", "Set SMS text mode"),
            ("AT+CSCS=\"GSM\"", "Set character set to GSM"),
            ("AT+CNMI=1,2,0,1,0", "Configure new message indications"),
        ];

        for (cmd, description) in init_commands {
            info!("📤 Sending AT command: {} ({})", cmd, description);
            match self.send_at_command(port, cmd).await {
                Ok(response) => {
                    info!("📥 AT command '{}' successful [size={}]: {}", cmd, response.len(), response);
                }
                Err(e) => {
                    error!("❌ AT command '{}' failed: {}", cmd, e);
                    return Err(anyhow!("Failed to initialize modem with command '{}': {}", cmd, e));
                }
            }
            sleep(Duration::from_millis(500)).await;
        }

        // Get comprehensive modem information
        self.retrieve_modem_info(port).await;

        info!("✅ GSM modem initialized successfully: {}", self.device_path);
        Ok(())
    }

    /// Retrieve comprehensive modem and SIM information
    async fn retrieve_modem_info(&mut self, port: &mut SerialStream) {
        info!("🔍 Retrieving modem and SIM information...");

        // Get SIM ID using multiple methods
        self.get_sim_id_comprehensive(port).await;

        // Get additional modem information
        self.get_modem_details(port).await;
    }

    /// Get SIM ID using multiple AT commands for better compatibility
    async fn get_sim_id_comprehensive(&mut self, port: &mut SerialStream) {
        let sim_commands = vec![
            ("AT+CIMI", "Get SIM IMSI"),
            ("AT+CCID", "Get SIM ICCID"),
        ];

        for (cmd, description) in sim_commands {
            info!("📤 Trying SIM command: {} ({})", cmd, description);
            match self.send_at_command(port, cmd).await {
                Ok(response) => {
                    info!("📥 SIM command '{}' response: {}", cmd, response);

                    if let Ok(sim_id) = self.parse_sim_id(&response, cmd).await {
                        self.sim_id = Some(sim_id.clone());
                        let _ = self.event_sender.send((GsmEventType::SimId, None));
                        info!("📱 SIM ID retrieved successfully: {} (via {})", sim_id, cmd);
                        return; // Stop trying other commands once we get a valid SIM ID
                    }
                }
                Err(e) => {
                    warn!("⚠️  SIM command '{}' failed: {}", cmd, e);
                }
            }
            sleep(Duration::from_millis(300)).await;
        }

        warn!("⚠️  Could not retrieve SIM ID for {} using any method", self.device_path);
    }

    /// Get additional modem information
    async fn get_modem_details(&self, port: &mut SerialStream) {
        let info_commands = vec![
            ("ATI", "Modem identification"),
            ("AT+CGMI", "Manufacturer identification"),
            ("AT+CGMM", "Model identification"),
            ("AT+CGMR", "Revision identification"),
            ("AT+CGSN", "Serial number"),
            ("AT+CSQ", "Signal quality"),
            ("AT+CREG?", "Network registration status"),
            ("AT+COPS?", "Current operator"),
        ];

        for (cmd, description) in info_commands {
            match self.send_at_command(port, cmd).await {
                Ok(response) => {
                    info!("📋 {}: {}", description, response);
                }
                Err(e) => {
                    debug!("Could not get {}: {}", description, e);
                }
            }
            sleep(Duration::from_millis(200)).await;
        }
    }

    async fn send_at_command(&self, port: &mut SerialStream, command: &str) -> Result<String> {
        let cmd_with_cr = format!("{}\r\n", command);
        port.write_all(cmd_with_cr.as_bytes()).await?;
        
        // Read response
        let mut reader = BufReader::new(port);
        let mut response = String::new();
        
        match timeout(Duration::from_secs(5), reader.read_line(&mut response)).await {
            Ok(Ok(_)) => {
                debug!("AT command '{}' response: {}", command, response.trim());
                Ok(response.trim().to_string())
            }
            Ok(Err(e)) => Err(anyhow!("Failed to read AT command response: {}", e)),
            Err(_) => Err(anyhow!("AT command timeout")),
        }
    }

    /// Legacy method for backward compatibility
    async fn get_sim_id(&self, port: &mut SerialStream) -> Result<String> {
        let response = self.send_at_command(port, "AT+CCID").await?;
        self.parse_sim_id(&response, "AT+CCID").await
    }

    /// Enhanced SIM ID parsing that handles different response formats
    async fn parse_sim_id(&self, response: &str, command: &str) -> Result<String> {
        // Different patterns for different commands and modem types
        let patterns = vec![
            // Standard ICCID format (starts with 89, 19-20 digits)
            r"89\d{17,18}",
            // IMSI format (15 digits)
            r"\d{15}",
            // ICCID with quotes or other formatting
            r#""(89\d{17,18})""#,
            // Some modems return with +CCID: prefix
            r"\+CCID:\s*(89\d{17,18})",
        ];

        for pattern in patterns {
            let regex = Regex::new(pattern).unwrap();
            if let Some(captures) = regex.captures(response) {
                let sim_id = if captures.len() > 1 {
                    // Use the captured group if available
                    captures.get(1).unwrap().as_str()
                } else {
                    // Use the full match
                    captures.get(0).unwrap().as_str()
                };

                // Validate the SIM ID
                if self.validate_sim_id(sim_id, command) {
                    return Ok(sim_id.to_string());
                }
            }
        }

        Err(anyhow!("Could not parse valid SIM ID from {} response: {}", command, response))
    }

    /// Validate SIM ID format
    fn validate_sim_id(&self, sim_id: &str, command: &str) -> bool {
        match command {
            "AT+CCID" | "AT^ICCID?" => {
                // ICCID should start with 89 and be 19-20 digits
                sim_id.starts_with("89") && sim_id.len() >= 19 && sim_id.len() <= 20 && sim_id.chars().all(|c| c.is_ascii_digit())
            }
            "AT+CIMI" => {
                // IMSI should be exactly 15 digits
                sim_id.len() == 15 && sim_id.chars().all(|c| c.is_ascii_digit())
            }
            _ => {
                // Generic validation: at least 10 digits
                sim_id.len() >= 10 && sim_id.chars().all(|c| c.is_ascii_digit())
            }
        }
    }

    /// Message reading loop with receiver-first approach
    async fn read_messages_loop_with_init(&mut self, port: &mut SerialStream) -> Result<()> {
        use tokio::time::{interval, timeout, Duration};
        use tokio::sync::mpsc;

        // Create a persistent BufReader to maintain buffer state throughout the connection
        let mut reader = BufReader::new(port);
        let mut line = String::new();

        // Create command channel for sending AT commands
        let (cmd_sender, mut cmd_receiver) = mpsc::unbounded_channel::<(String, String)>(); // (command, description)

        // Start initialization in background - receiver will handle responses
        let init_sender = cmd_sender.clone();
        let device_path_clone = self.device_path.clone();
        tokio::spawn(async move {
            info!("🔧 Starting GSM modem initialization: {}", device_path_clone);

            // Send initialization commands
            let init_commands = vec![
                ("AT", "Basic connectivity test"),
                ("ATE0", "Turn off echo"),
                ("AT+CMGF=0", "Set SMS PDU mode"),
                ("AT+CNMI=1,2,0,1,0", "Configure new message indications"),
                ("AT+CIMI", "Get SIM IMSI"),
                ("AT+CCID", "Get SIM ICCID"),
            ];

            // Wait a bit for receiver to be ready
            tokio::time::sleep(Duration::from_millis(100)).await;

            for (cmd, description) in init_commands {
                info!("📤 Queuing AT command: {} ({})", cmd, description);
                if let Err(e) = init_sender.send((cmd.to_string(), description.to_string())) {
                    error!("Failed to queue command {}: {}", cmd, e);
                }
                tokio::time::sleep(Duration::from_millis(500)).await;
            }

            info!("✅ Initialization commands queued");
        });

        // Setup heartbeat
        let mut heartbeat_interval = interval(self.heartbeat_interval);
        let heartbeat_sender = cmd_sender.clone();

        info!("💓 Starting receiver-first approach with heartbeat every {} seconds", self.heartbeat_interval.as_secs());

        // Skip the first immediate tick
        heartbeat_interval.tick().await;

        loop {
            // Use select to handle commands, heartbeat, and incoming data
            tokio::select! {
                // Handle command queue (initialization and other commands)
                cmd_result = cmd_receiver.recv() => {
                    match cmd_result {
                        Some((command, description)) => {
                            info!("📤 Sending AT command: {} ({})", command, description);

                            // Send the command
                            let port_ref = reader.get_mut();
                            if let Err(e) = self.send_at_command_raw(port_ref, &command).await {
                                error!("❌ Failed to send AT command '{}': {}", command, e);
                            } else {
                                debug!("📤 AT command '{}' sent successfully", command);
                            }
                        }
                        None => {
                            debug!("Command channel closed");
                        }
                    }
                }

                // Handle heartbeat
                _ = heartbeat_interval.tick() => {
                    info!("💓 Sending heartbeat AT command to {}", self.device_path);
                    if let Err(e) = heartbeat_sender.send(("AT".to_string(), "Heartbeat".to_string())) {
                        warn!("💔 Failed to queue heartbeat command: {}", e);
                    }
                }

                // Handle incoming data - this is the main receiver
                read_result = timeout(Duration::from_millis(100), reader.read_line(&mut line)) => {
                    match read_result {
                        Ok(Ok(0)) => {
                            // EOF reached
                            info!("📡 Connection closed (EOF)");
                            break;
                        }
                        Ok(Ok(_)) => {
                            let trimmed_line = line.trim();

                            // Show all non-empty responses
                            if !trimmed_line.is_empty() {
                                info!("📥 Received: '{}'", trimmed_line);

                                // Handle different types of responses
                                self.handle_at_response(trimmed_line).await;
                            }

                            // Check for SMS indication (PDU mode)
                            if trimmed_line.starts_with("+CMT:") {
                                if let Ok(message) = self.parse_sms_message_pdu(trimmed_line, &mut reader).await {
                                    let _ = self.event_sender.send((GsmEventType::NewMessage, Some(vec![message])));
                                }
                            }

                            line.clear();
                        }
                        Ok(Err(e)) => {
                            error!("❌ Error reading from serial port: {}", e);
                            return Err(anyhow!("Serial read error: {}", e));
                        }
                        Err(_) => {
                            // Timeout - continue to next iteration
                            continue;
                        }
                    }
                }
            }
        }

        let _ = self.event_sender.send((GsmEventType::Close, None));
        Ok(())
    }

    /// Initialize modem using persistent BufReader
    async fn initialize_modem_with_reader(&mut self, reader: &mut BufReader<&mut SerialStream>) -> Result<()> {
        // Send basic AT commands to initialize the modem
        let init_commands = vec![
            ("AT", "Basic connectivity test"),
            ("ATE0", "Turn off echo"),
            ("AT+CMGF=0", "Set SMS PDU mode"),
            ("AT+CNMI=1,2,0,1,0", "Configure new message indications"),
        ];

        for (cmd, description) in init_commands {
            info!("📤 Sending AT command: {} ({})", cmd, description);
            match self.send_at_command_with_reader(reader, cmd).await {
                Ok(response) => {
                    info!("📥 AT command '{}' successful: {}", cmd, response);
                }
                Err(e) => {
                    error!("❌ AT command '{}' failed: {}", cmd, e);
                    return Err(anyhow!("Failed to initialize modem with command '{}': {}", cmd, e));
                }
            }
            sleep(Duration::from_millis(500)).await;
        }

        // Get comprehensive modem information
        self.retrieve_modem_info_with_reader(reader).await;

        info!("✅ GSM modem initialized successfully: {}", self.device_path);
        Ok(())
    }

    /// Send AT command using persistent BufReader
    async fn send_at_command_with_reader(&self, reader: &mut BufReader<&mut SerialStream>, command: &str) -> Result<String> {
        let cmd_with_cr = format!("{}\r\n", command);

        // Get the underlying port to write
        let port = reader.get_mut();
        port.write_all(cmd_with_cr.as_bytes()).await?;
        port.flush().await?;

        // Read response using the same reader
        let mut response = String::new();

        match timeout(Duration::from_secs(5), reader.read_line(&mut response)).await {
            Ok(Ok(_)) => {
                debug!("AT command '{}' response: {}", command, response.trim());
                Ok(response.trim().to_string())
            }
            Ok(Err(e)) => Err(anyhow!("Failed to read AT command response: {}", e)),
            Err(_) => Err(anyhow!("AT command timeout")),
        }
    }

    /// Get modem info using persistent reader
    async fn retrieve_modem_info_with_reader(&mut self, reader: &mut BufReader<&mut SerialStream>) {
        info!("🔍 Retrieving modem and SIM information...");

        // Get SIM ID using multiple methods
        self.get_sim_id_comprehensive_with_reader(reader).await;

        // Get additional modem information
        self.get_modem_details_with_reader(reader).await;
    }

    /// Get SIM ID using persistent reader
    async fn get_sim_id_comprehensive_with_reader(&mut self, reader: &mut BufReader<&mut SerialStream>) {
        let sim_commands = vec![
            ("AT+CIMI", "Get SIM IMSI"),
            ("AT+CCID", "Get SIM ICCID"),
        ];

        for (cmd, description) in sim_commands {
            info!("📤 Trying SIM command: {} ({})", cmd, description);
            match self.send_at_command_with_reader(reader, cmd).await {
                Ok(response) => {
                    info!("📥 SIM command '{}' response: {}", cmd, response);

                    if let Ok(sim_id) = self.parse_sim_id(&response, cmd).await {
                        self.sim_id = Some(sim_id.clone());
                        let _ = self.event_sender.send((GsmEventType::SimId, None));
                        info!("📱 SIM ID retrieved successfully: {} (via {})", sim_id, cmd);
                        return; // Stop trying other commands once we get a valid SIM ID
                    }
                }
                Err(e) => {
                    warn!("⚠️  SIM command '{}' failed: {}", cmd, e);
                }
            }
            sleep(Duration::from_millis(300)).await;
        }

        warn!("⚠️  Could not retrieve SIM ID for {} using any method", self.device_path);
    }

    /// Get modem details using persistent reader
    async fn get_modem_details_with_reader(&self, reader: &mut BufReader<&mut SerialStream>) {
        let info_commands = vec![
            ("ATI", "Modem identification"),
            ("AT+CGMI", "Manufacturer identification"),
            ("AT+CGMM", "Model identification"),
            ("AT+CGMR", "Revision identification"),
            ("AT+CGSN", "Serial number"),
            ("AT+CSQ", "Signal quality"),
            ("AT+CREG?", "Network registration status"),
            ("AT+COPS?", "Current operator"),
        ];

        for (cmd, description) in info_commands {
            match self.send_at_command_with_reader(reader, cmd).await {
                Ok(response) => {
                    info!("📋 {}: {}", description, response);
                }
                Err(e) => {
                    debug!("Could not get {}: {}", description, e);
                }
            }
            sleep(Duration::from_millis(200)).await;
        }
    }

    /// Legacy method - redirect to new implementation
    async fn read_messages_loop_simple(&mut self, port: &mut SerialStream) -> Result<()> {
        // Redirect to the new receiver-first implementation
        self.read_messages_loop_with_init(port).await
    }

    /// Send raw AT command without expecting immediate response
    async fn send_at_command_raw(&self, port: &mut SerialStream, command: &str) -> Result<()> {
        let cmd_with_cr = format!("{}\r\n", command);
        port.write_all(cmd_with_cr.as_bytes()).await
            .map_err(|e| anyhow!("Failed to write AT command: {}", e))?;

        // Flush to ensure the command is sent immediately
        port.flush().await
            .map_err(|e| anyhow!("Failed to flush AT command: {}", e))?;

        Ok(())
    }

    /// Handle AT command responses
    async fn handle_at_response(&mut self, response: &str) {
        // Handle different types of responses
        if response == "OK" {
            debug!("✅ AT command successful");
        } else if response == "ERROR" {
            warn!("❌ AT command failed");
        } else if response.starts_with("+") {
            // Extended response codes
            debug!("📋 Extended response: {}", response);

            // Handle specific responses
            if response.starts_with("+CIMI:") || (response.len() == 15 && response.chars().all(|c| c.is_ascii_digit())) {
                // IMSI response
                if response.len() == 15 && response.chars().all(|c| c.is_ascii_digit()) {
                    self.sim_id = Some(response.to_string());
                    let _ = self.event_sender.send((GsmEventType::SimId, None));
                    info!("📱 SIM IMSI retrieved: {}", response);
                }
            } else if response.starts_with("+CCID:") || (response.starts_with("89") && response.len() >= 19) {
                // ICCID response
                if let Some(sim_id) = self.extract_sim_id_from_response(response) {
                    self.sim_id = Some(sim_id.clone());
                    let _ = self.event_sender.send((GsmEventType::SimId, None));
                    info!("📱 SIM ICCID retrieved: {}", sim_id);
                }
            }
        } else if !response.is_empty() && response.chars().all(|c| c.is_ascii_digit()) && response.len() == 15 {
            // Raw IMSI (15 digits)
            self.sim_id = Some(response.to_string());
            let _ = self.event_sender.send((GsmEventType::SimId, None));
            info!("📱 SIM IMSI retrieved: {}", response);
        } else if !response.is_empty() && response.starts_with("89") && response.len() >= 19 {
            // Raw ICCID (starts with 89, 19-20 digits)
            self.sim_id = Some(response.to_string());
            let _ = self.event_sender.send((GsmEventType::SimId, None));
            info!("📱 SIM ICCID retrieved: {}", response);
        } else if !response.is_empty() {
            // Other responses (manufacturer, model, etc.)
            debug!("📋 Modem info: {}", response);
        }
    }

    /// Extract SIM ID from various response formats
    fn extract_sim_id_from_response(&self, response: &str) -> Option<String> {
        // Try different patterns
        let patterns = vec![
            r"89\d{17,18}",           // Standard ICCID
            r"\+CCID:\s*(89\d{17,18})", // +CCID: format
            r#""(89\d{17,18})""#,     // Quoted format
        ];

        for pattern in patterns {
            if let Ok(regex) = Regex::new(pattern) {
                if let Some(captures) = regex.captures(response) {
                    let sim_id = if captures.len() > 1 {
                        captures.get(1).unwrap().as_str()
                    } else {
                        captures.get(0).unwrap().as_str()
                    };

                    if sim_id.starts_with("89") && sim_id.len() >= 19 && sim_id.len() <= 20 {
                        return Some(sim_id.to_string());
                    }
                }
            }
        }
        None
    }

    /// Parse SMS message from PDU format
    async fn parse_sms_message_pdu(&self, header_line: &str, reader: &mut BufReader<&mut SerialStream>) -> Result<SmsMessage> {
        // Parse PDU header: +CMT: ,<length>
        // Example: +CMT: ,24
        let header_regex = Regex::new(r"\+CMT:\s*,(\d+)").unwrap();

        let pdu_length = if let Some(captures) = header_regex.captures(header_line) {
            captures.get(1).unwrap().as_str().parse::<usize>()
                .map_err(|e| anyhow!("Invalid PDU length: {}", e))?
        } else {
            return Err(anyhow!("Could not parse PDU header: {}", header_line));
        };

        // Read the PDU data (next line)
        let mut pdu_line = String::new();
        reader.read_line(&mut pdu_line).await?;
        let pdu_data = pdu_line.trim().to_string();

        info!("📱 Received SMS PDU (length: {}): {}", pdu_length, pdu_data);

        // Parse the PDU data
        match self.parse_pdu_data(&pdu_data).await {
            Ok(message) => {
                info!("📱 SMS parsed successfully from: {} - {}", message.sender, message.content);
                Ok(message)
            }
            Err(e) => {
                warn!("⚠️  Failed to parse PDU data: {}", e);
                // Return a basic message with the raw PDU for debugging
                Ok(SmsMessage {
                    sender: "UNKNOWN".to_string(),
                    content: format!("RAW_PDU: {}", pdu_data),
                    received_at: Utc::now(),
                })
            }
        }
    }

    /// Parse PDU data into SMS message components
    async fn parse_pdu_data(&self, pdu_hex: &str) -> Result<SmsMessage> {
        // Basic PDU parsing implementation
        // PDU format: SMSC_Length + SMSC + PDU_Type + Sender_Length + Sender + PID + DCS + Timestamp + UDL + UD

        if pdu_hex.len() < 20 {
            return Err(anyhow!("PDU data too short: {}", pdu_hex));
        }

        let mut pos = 0;

        // Parse SMSC length (1 byte)
        let smsc_length = self.hex_to_u8(&pdu_hex[pos..pos+2])? as usize;
        pos += 2;

        // Skip SMSC (smsc_length * 2 hex chars)
        pos += smsc_length * 2;

        // Parse PDU type (1 byte)
        let _pdu_type = self.hex_to_u8(&pdu_hex[pos..pos+2])?;
        pos += 2;

        // Parse sender address length (1 byte)
        let sender_length = self.hex_to_u8(&pdu_hex[pos..pos+2])? as usize;
        pos += 2;

        // Parse sender address type (1 byte)
        let _sender_type = self.hex_to_u8(&pdu_hex[pos..pos+2])?;
        pos += 2;

        // Parse sender address (variable length, padded to even number of digits)
        let sender_hex_length = if sender_length % 2 == 0 { sender_length } else { sender_length + 1 };
        if pos + sender_hex_length > pdu_hex.len() {
            return Err(anyhow!("PDU data truncated at sender address"));
        }

        let sender_hex = &pdu_hex[pos..pos+sender_hex_length];
        let sender = self.decode_phone_number(sender_hex, sender_length)?;
        pos += sender_hex_length;

        // Skip PID (1 byte) and DCS (1 byte)
        pos += 4;

        // Skip timestamp (7 bytes)
        pos += 14;

        // Parse User Data Length (1 byte)
        if pos + 2 > pdu_hex.len() {
            return Err(anyhow!("PDU data truncated at UDL"));
        }
        let udl = self.hex_to_u8(&pdu_hex[pos..pos+2])? as usize;
        pos += 2;

        // Parse User Data
        let remaining_hex = &pdu_hex[pos..];
        let content = self.decode_user_data(remaining_hex, udl)?;

        Ok(SmsMessage {
            sender,
            content,
            received_at: Utc::now(),
        })
    }

    /// Convert hex string to u8
    fn hex_to_u8(&self, hex: &str) -> Result<u8> {
        u8::from_str_radix(hex, 16).map_err(|e| anyhow!("Invalid hex: {} - {}", hex, e))
    }

    /// Decode phone number from PDU format (swap nibbles)
    fn decode_phone_number(&self, hex: &str, length: usize) -> Result<String> {
        let mut phone = String::new();
        let chars: Vec<char> = hex.chars().collect();

        for i in (0..hex.len()).step_by(2) {
            if i + 1 < chars.len() {
                // Swap nibbles: "12" becomes "21"
                phone.push(chars[i + 1]);
                phone.push(chars[i]);
            }
        }

        // Remove padding 'F' and truncate to actual length
        phone = phone.replace('F', "").replace('f', "");
        if phone.len() > length {
            phone.truncate(length);
        }

        Ok(phone)
    }

    /// Decode user data (simplified - assumes 7-bit encoding)
    fn decode_user_data(&self, hex: &str, _udl: usize) -> Result<String> {
        // For now, try to decode as 7-bit GSM encoding
        // This is a simplified implementation
        let mut content = String::new();

        // Convert hex to bytes
        let mut bytes = Vec::new();
        for i in (0..hex.len()).step_by(2) {
            if i + 1 < hex.len() {
                let byte_hex = &hex[i..i+2];
                match u8::from_str_radix(byte_hex, 16) {
                    Ok(byte) => bytes.push(byte),
                    Err(_) => break,
                }
            }
        }

        // Simple ASCII conversion (not proper 7-bit unpacking)
        for byte in bytes {
            if byte >= 32 && byte <= 126 {
                content.push(byte as char);
            } else if byte == 0 {
                break;
            } else {
                content.push('?'); // Unknown character
            }
        }

        if content.is_empty() {
            content = format!("HEX: {}", hex);
        }

        Ok(content)
    }

    /// Create PDU data for SMS sending
    fn create_pdu(&self, phone_number: &str, message: &str) -> Result<String> {
        let mut pdu = String::new();

        // SMSC (Service Center) - use default (empty)
        pdu.push_str("00"); // SMSC length = 0 (use default)

        // PDU Type (SMS-SUBMIT)
        pdu.push_str("01"); // SMS-SUBMIT, no status report

        // Message Reference (let network assign)
        pdu.push_str("00");

        // Destination Address (phone number)
        let encoded_number = self.encode_phone_number(phone_number)?;
        pdu.push_str(&encoded_number);

        // Protocol Identifier
        pdu.push_str("00");

        // Data Coding Scheme (7-bit default alphabet)
        pdu.push_str("00");

        // User Data Length and User Data
        let encoded_message = self.encode_message_7bit(message)?;
        pdu.push_str(&encoded_message);

        Ok(pdu)
    }

    /// Calculate PDU length (excluding SMSC part)
    fn calculate_pdu_length(&self, pdu: &str) -> Result<u8> {
        // PDU length is the number of bytes after SMSC length field
        // SMSC is first byte (00), so we count everything after that
        if pdu.len() < 2 {
            return Err(anyhow!("PDU too short"));
        }

        let pdu_without_smsc = &pdu[2..]; // Skip SMSC length byte
        let length = pdu_without_smsc.len() / 2; // Convert hex chars to bytes

        Ok(length as u8)
    }

    /// Encode phone number for PDU format
    fn encode_phone_number(&self, phone_number: &str) -> Result<String> {
        // Clean phone number (remove spaces, dashes, etc.)
        let clean_number: String = phone_number.chars()
            .filter(|c| c.is_ascii_digit() || *c == '+')
            .collect();

        let (number_type, digits) = if clean_number.starts_with('+') {
            ("91", &clean_number[1..]) // International format
        } else {
            ("81", &clean_number[..])  // National format
        };

        // Address length (number of digits)
        let addr_length = format!("{:02X}", digits.len());

        // Encode digits with nibble swapping
        let mut encoded_digits = String::new();
        let digit_chars: Vec<char> = digits.chars().collect();

        for i in (0..digit_chars.len()).step_by(2) {
            if i + 1 < digit_chars.len() {
                // Swap nibbles: "12" becomes "21"
                encoded_digits.push(digit_chars[i + 1]);
                encoded_digits.push(digit_chars[i]);
            } else {
                // Odd number of digits, pad with F
                encoded_digits.push('F');
                encoded_digits.push(digit_chars[i]);
            }
        }

        Ok(format!("{}{}{}", addr_length, number_type, encoded_digits))
    }

    /// Encode message using 7-bit GSM encoding
    fn encode_message_7bit(&self, message: &str) -> Result<String> {
        // For simplicity, this implementation assumes ASCII characters
        // A full implementation would handle the complete GSM 7-bit character set

        let bytes: Vec<u8> = message.bytes().collect();
        let message_length = bytes.len();

        // User Data Length (number of characters, not bytes)
        let udl = format!("{:02X}", message_length);

        // Pack 7-bit characters into 8-bit bytes
        let packed_data = self.pack_7bit(&bytes)?;

        // Convert to hex string
        let hex_data: String = packed_data.iter()
            .map(|b| format!("{:02X}", b))
            .collect();

        Ok(format!("{}{}", udl, hex_data))
    }

    /// Pack 7-bit data into 8-bit bytes (GSM 7-bit packing)
    fn pack_7bit(&self, data: &[u8]) -> Result<Vec<u8>> {
        if data.is_empty() {
            return Ok(Vec::new());
        }

        let mut packed = Vec::new();
        let mut carry = 0u8;
        let mut carry_bits = 0u8;

        for (i, &byte) in data.iter().enumerate() {
            // Ensure we're working with 7-bit data
            let seven_bit = byte & 0x7F;

            // Calculate how many bits to shift
            let shift = i % 7;

            if shift == 0 {
                carry = seven_bit;
                carry_bits = 7;
            } else {
                // Combine with carry
                let combined = carry | ((seven_bit << carry_bits) & 0xFF);
                packed.push(combined);

                // Update carry
                carry_bits = (7 - shift) as u8;
                if carry_bits > 0 {
                    carry = seven_bit >> shift;
                } else {
                    carry = 0;
                    carry_bits = 0;
                }
            }
        }

        // Add remaining carry if any
        if carry_bits > 0 {
            packed.push(carry);
        }

        Ok(packed)
    }

    /// Legacy method - Parse SMS message from persistent reader (text mode)
    async fn parse_sms_message_from_reader(&self, header_line: &str, reader: &mut BufReader<&mut SerialStream>) -> Result<SmsMessage> {
        // Parse SMS header: +CMT: "sender",,"timestamp"
        let header_regex = Regex::new(r#"\+CMT:\s*"([^"]+)"[^"]*"([^"]+)""#).unwrap();

        let (sender, _timestamp_str) = if let Some(captures) = header_regex.captures(header_line) {
            let sender = captures.get(1).unwrap().as_str().to_string();
            let timestamp = captures.get(2).unwrap().as_str().to_string();
            (sender, timestamp)
        } else {
            return Err(anyhow!("Could not parse SMS header: {}", header_line));
        };

        // Read the message content (next line)
        let mut content_line = String::new();
        reader.read_line(&mut content_line).await?;
        let content = content_line.trim().to_string();

        Ok(SmsMessage {
            sender,
            content,
            received_at: Utc::now(), // Use current time for simplicity
        })
    }

    /// Parse SMS message from a single line (simplified)
    async fn parse_sms_message_from_line(&self, header_line: &str, port: &mut SerialStream) -> Result<SmsMessage> {
        // Parse SMS header: +CMT: "sender",,"timestamp"
        let header_regex = Regex::new(r#"\+CMT:\s*"([^"]+)"[^"]*"([^"]+)""#).unwrap();

        let (sender, _timestamp_str) = if let Some(captures) = header_regex.captures(header_line) {
            let sender = captures.get(1).unwrap().as_str().to_string();
            let timestamp = captures.get(2).unwrap().as_str().to_string();
            (sender, timestamp)
        } else {
            return Err(anyhow!("Could not parse SMS header: {}", header_line));
        };

        // Read the message content (next line)
        let mut content_line = String::new();
        let mut reader = BufReader::new(port);
        reader.read_line(&mut content_line).await?;
        let content = content_line.trim().to_string();

        Ok(SmsMessage {
            sender,
            content,
            received_at: Utc::now(), // Use current time for simplicity
        })
    }



    /// Legacy method for backward compatibility
    async fn read_messages_loop(&mut self, port: &mut SerialStream) -> Result<()> {
        // Redirect to the new receiver-first implementation
        self.read_messages_loop_with_init(port).await
    }


    async fn parse_sms_message(&self, header_line: &str, reader: &mut BufReader<&mut SerialStream>) -> Result<SmsMessage> {
        // Parse SMS header: +CMT: "sender",,"timestamp"
        let header_regex = Regex::new(r#"\+CMT:\s*"([^"]+)"[^"]*"([^"]+)""#).unwrap();
        
        let (sender, timestamp_str) = if let Some(captures) = header_regex.captures(header_line) {
            let sender = captures.get(1).unwrap().as_str().to_string();
            let timestamp = captures.get(2).unwrap().as_str().to_string();
            (sender, timestamp)
        } else {
            return Err(anyhow!("Could not parse SMS header: {}", header_line));
        };

        // Read the message content (next line)
        let mut content_line = String::new();
        reader.read_line(&mut content_line).await?;
        let content = content_line.trim().to_string();

        // Parse timestamp (GSM format: "yy/MM/dd,HH:mm:ss±zz")
        let received_at = self.parse_gsm_timestamp(&timestamp_str)
            .unwrap_or_else(|_| Utc::now());

        Ok(SmsMessage {
            sender,
            content,
            received_at,
        })
    }

    fn parse_gsm_timestamp(&self, timestamp_str: &str) -> Result<DateTime<Utc>> {
        // GSM timestamp format: "yy/MM/dd,HH:mm:ss±zz"
        // For simplicity, we'll just use current time
        // In a production system, you'd want to properly parse this
        Ok(Utc::now())
    }
}
