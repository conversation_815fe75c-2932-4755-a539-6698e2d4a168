use anyhow::{anyhow, Result};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::mpsc;
use tokio::time::{interval, sleep};
use tracing::{error, info, warn};

use crate::config_types::AppConfig;
use crate::serial::scanner::SerialScanner;

use super::entity::GsmEntity;
use super::types::{CreateMessageDto, GsmEventType, GsmModemInfo, SmsMessage};

pub struct GsmService {
    config: AppConfig,
    modem_map: HashMap<String, GsmModemInfo>,
    event_receiver: mpsc::UnboundedReceiver<(String, GsmEventType, Option<Vec<SmsMessage>>)>,
    event_sender: mpsc::UnboundedSender<(String, GsmEventType, Option<Vec<SmsMessage>>)>,
}

impl GsmService {
    pub async fn new(config: AppConfig) -> Result<Self> {
        let (event_sender, event_receiver) = mpsc::unbounded_channel();

        Ok(Self {
            config,
            modem_map: HashMap::new(),
            event_receiver,
            event_sender,
        })
    }

    /// Manually trigger an immediate device scan for faster auto-connection
    pub async fn trigger_device_scan(&self) -> Result<Vec<String>> {
        info!("🔍 Manual device scan triggered");

        match SerialScanner::scan_gsm_devices().await {
            Ok(devices) => {
                let device_keys: Vec<String> = devices.iter()
                    .map(|d| d.device_hash())
                    .collect();

                info!("📱 Manual scan found {} GSM devices: {:?}", devices.len(), device_keys);
                Ok(device_keys)
            }
            Err(e) => {
                error!("❌ Manual device scan failed: {}", e);
                Err(e)
            }
        }
    }

    /// Send SMS via any available GSM device
    pub async fn send_sms(&self, phone_number: &str, message: &str) -> Result<String> {
        info!("📤 Attempting to send SMS to {} via any available device", phone_number);

        if self.modem_map.is_empty() {
            return Err(anyhow!("No GSM devices available for sending SMS"));
        }

        // Try to send via the first available device
        for (device_key, modem_info) in &self.modem_map {
            info!("📱 Trying to send SMS via device: {} ({})", device_key, modem_info.device_path);

            // Create a temporary event sender for SMS sending
            let (temp_sender, mut _temp_receiver) = mpsc::unbounded_channel();

            // Create a temporary GSM entity for sending
            let gsm_entity = GsmEntity::new(
                modem_info.device_path.clone(),
                self.config.gsm.baud_rate,
                self.config.gsm.timeout_seconds,
                self.config.gsm.heartbeat_interval_seconds,
                temp_sender,
            );

            match gsm_entity.send_sms_pdu(phone_number, message).await {
                Ok(response) => {
                    info!("✅ SMS sent successfully via {}: {}", device_key, response);
                    return Ok(response);
                }
                Err(e) => {
                    warn!("❌ Failed to send SMS via {}: {}", device_key, e);
                    continue; // Try next device
                }
            }
        }

        Err(anyhow!("Failed to send SMS via any available GSM device"))
    }

    /// Send SMS via specific device
    pub async fn send_sms_via_device(&self, device_key: &str, phone_number: &str, message: &str) -> Result<String> {
        info!("📤 Attempting to send SMS to {} via device: {}", phone_number, device_key);

        let modem_info = self.modem_map.get(device_key)
            .ok_or_else(|| anyhow!("Device not found: {}", device_key))?;

        // Create a temporary event sender for SMS sending
        let (temp_sender, mut _temp_receiver) = mpsc::unbounded_channel();

        // Create a temporary GSM entity for sending
        let gsm_entity = GsmEntity::new(
            modem_info.device_path.clone(),
            self.config.gsm.baud_rate,
            self.config.gsm.timeout_seconds,
            self.config.gsm.heartbeat_interval_seconds,
            temp_sender,
        );

        match gsm_entity.send_sms_pdu(phone_number, message).await {
            Ok(response) => {
                info!("✅ SMS sent successfully via {}: {}", device_key, response);
                Ok(response)
            }
            Err(e) => {
                error!("❌ Failed to send SMS via {}: {}", device_key, e);
                Err(e)
            }
        }
    }

    pub async fn start(mut self) -> Result<()> {
        info!("Starting GSM service");

        // Start device scanning task
        let scan_sender = self.event_sender.clone();
        let scan_interval = self.config.gsm.scan_interval_seconds;
        let config_clone = self.config.clone();
        
        tokio::spawn(async move {
            Self::device_scan_task(scan_sender, scan_interval, config_clone).await;
        });

        // Process events
        while let Some((device_key, event_type, messages)) = self.event_receiver.recv().await {
            match event_type {
                GsmEventType::NewMessage => {
                    if let Some(messages) = messages {
                        self.handle_new_messages(&device_key, messages).await;
                    }
                }
                GsmEventType::Open => {
                    info!("GSM modem opened: {}", device_key);
                }
                GsmEventType::Close => {
                    info!("GSM modem closed: {}", device_key);
                    self.modem_map.remove(&device_key);
                }
                GsmEventType::Error => {
                    warn!("GSM modem error: {}", device_key);
                }
                GsmEventType::SimId => {
                    info!("SIM ID updated for device: {}", device_key);
                },
                GsmEventType::AtCommandResponse | GsmEventType::ModemInfo => todo!()
            }
        }

        Ok(())
    }

    async fn device_scan_task(
        event_sender: mpsc::UnboundedSender<(String, GsmEventType, Option<Vec<SmsMessage>>)>,
        scan_interval_seconds: u64,
        config: AppConfig,
    ) {
        let mut interval = interval(Duration::from_secs(scan_interval_seconds));
        let mut active_devices: HashMap<String, tokio::task::JoinHandle<()>> = HashMap::new();
        let mut last_scan_devices: Vec<String> = Vec::new();

        loop {
            interval.tick().await;
            info!("Scanning for GSM devices...");

            match SerialScanner::scan_gsm_devices().await {
                Ok(devices) => {
                    info!("Found {} GSM devices", devices.len());
                    let mut current_device_keys = Vec::new();

                    // Start new devices immediately
                    for device in devices {
                        let device_key = device.device_hash();
                        current_device_keys.push(device_key.clone());

                        if !active_devices.contains_key(&device_key) {
                            info!("🔌 NEW GSM DEVICE DETECTED - Auto-connecting: {} at {}", device_key, device.path);

                            // Immediately attempt connection
                            Self::connect_new_device(
                                device,
                                device_key.clone(),
                                event_sender.clone(),
                                config.clone(),
                                &mut active_devices,
                            ).await;
                        }
                    }

                    // Check for newly connected devices (not in last scan)
                    for device_key in &current_device_keys {
                        if !last_scan_devices.contains(device_key) && active_devices.contains_key(device_key) {
                            info!("✅ GSM device successfully auto-connected: {}", device_key);
                        }
                    }

                    // Remove disconnected devices
                    let mut to_remove = Vec::new();
                    for device_key in active_devices.keys() {
                        if !current_device_keys.contains(device_key) {
                            info!("🔌 GSM device disconnected: {}", device_key);
                            to_remove.push(device_key.clone());
                        }
                    }

                    for device_key in to_remove {
                        if let Some(handle) = active_devices.remove(&device_key) {
                            handle.abort();
                            info!("🗑️  Cleaned up disconnected device: {}", device_key);
                        }
                    }

                    // Update last scan state
                    last_scan_devices = current_device_keys;
                }
                Err(e) => {
                    error!("❌ Error scanning for GSM devices: {}", e);
                    // Continue scanning even on errors
                }
            }
        }
    }

    async fn connect_new_device(
        device: crate::gsm::types::DeviceInfo,
        device_key: String,
        event_sender: mpsc::UnboundedSender<(String, GsmEventType, Option<Vec<SmsMessage>>)>,
        config: AppConfig,
        active_devices: &mut HashMap<String, tokio::task::JoinHandle<()>>,
    ) {
        info!("🚀 Initiating connection to GSM device: {} at {}", device_key, device.path);

        // Create event sender for this specific device
        let device_event_sender = event_sender.clone();
        let device_key_clone = device_key.clone();
        let device_path = device.path.clone();
        let baud_rate = config.gsm.baud_rate;
        let timeout_seconds = config.gsm.timeout_seconds;

        let (device_sender, mut device_receiver) = mpsc::unbounded_channel();

        // Spawn task to handle this device's events
        let _event_forwarder = tokio::spawn(async move {
            while let Some((event_type, messages)) = device_receiver.recv().await {
                let _ = device_event_sender.send((device_key_clone.clone(), event_type, messages));
            }
        });

        // Spawn GSM entity task with enhanced error handling
        let gsm_task = tokio::spawn(async move {
            info!("📡 Starting GSM entity for device: {}", device_path);

            let mut gsm_entity = GsmEntity::new(
                device_path.clone(),
                baud_rate,
                timeout_seconds,
                config.gsm.heartbeat_interval_seconds,
                device_sender,
            );

            if let Err(e) = gsm_entity.start().await {
                error!("❌ GSM entity failed for {}: {}", device_path, e);
            } else {
                info!("✅ GSM entity completed for {}", device_path);
            }
        });

        active_devices.insert(device_key, gsm_task);
    }

    async fn handle_new_messages(&mut self, device_key: &str, messages: Vec<SmsMessage>) {
        // for message in messages {
        //     info!(
        //         "New SMS from {} on device {}: {}",
        //         message.sender, device_key, message.content
        //     );
        //
        //     // Get SIM ID if available
        //     let sim_id = self.modem_map.get(device_key).and_then(|info| info.sim_id.clone());
        //
        //     let create_message_dto = CreateMessageDto {
        //         sim_id,
        //         device_key: if sim_id.is_none() { Some(device_key.to_string()) } else { None },
        //         content: message.content,
        //         received_at: message.received_at,
        //         sender: message.sender,
        //     };
        //
        //     // Send to Kafka
        //     if let Err(e) = self.kafka_client.send_message(&create_message_dto).await {
        //         error!("Failed to send message to Kafka: {}", e);
        //     } else {
        //         info!("Message sent to Kafka successfully");
        //     }
        // }
    }
}
