use serde::Deserialize;

#[derive(Debug, <PERSON><PERSON>, Deserialize)]
pub struct AppConfig {
    #[serde(default)]
    pub gsm: GsmConfig,
}

#[derive(Debug, Clone, Deserialize)]
pub struct GsmConfig {
    #[serde(default = "default_scan_interval")]
    pub scan_interval_seconds: u64,
    #[serde(default = "default_baud_rate")]
    pub baud_rate: u32,
    #[serde(default = "default_timeout")]
    pub timeout_seconds: u64,
    #[serde(default = "default_heartbeat_interval")]
    pub heartbeat_interval_seconds: u64,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            gsm: GsmConfig::default(),
        }
    }
}

impl Default for GsmConfig {
    fn default() -> Self {
        Self {
            scan_interval_seconds: default_scan_interval(),
            baud_rate: default_baud_rate(),
            timeout_seconds: default_timeout(),
            heartbeat_interval_seconds: default_heartbeat_interval(),
        }
    }
}

fn default_scan_interval() -> u64 {
    30 // 30 seconds
}

fn default_baud_rate() -> u32 {
    115200 // Standard GSM modem baud rate
}

fn default_timeout() -> u64 {
    300 // 5 minutes
}

fn default_heartbeat_interval() -> u64 {
    10 // 10 seconds
}
