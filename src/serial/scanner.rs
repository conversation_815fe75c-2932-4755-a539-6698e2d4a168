use std::fmt::format;
use anyhow::Result;
use tokio_serial::{available_ports, SerialPortInfo, SerialPortType};
use tracing::{debug, info, warn};

use crate::gsm::types::{DeviceInfo, is_supported_modem};

pub struct SerialScanner;

impl SerialScanner {
    pub async fn scan_gsm_devices() -> Result<Vec<DeviceInfo>> {
        let ports = available_ports()?;
        info!("Found {} serial ports", ports.len());
        let mut gsm_devices = Vec::new();

        for port in ports {
            info!("Found serial port: {:?}", port);

            let device_info = match port.port_type {
                SerialPortType::UsbPort(usb_info) => {
                    DeviceInfo {
                        path: port.port_name,
                        vendor_id: Some(format!("{:04x}", usb_info.vid)),
                        product_id: Some(format!("{:04x}", usb_info.pid)),
                        serial_number: usb_info.serial_number,
                    }
                }
                _ => {
                    DeviceInfo {
                        path: port.port_name,
                        vendor_id: None,
                        product_id: None,
                        serial_number: None,
                    }
                }
            };

            // Check if this is a supported GSM modem
            if is_supported_modem(
                device_info.vendor_id.as_deref(),
                device_info.product_id.as_deref(),
            ) {
                debug!(
                    "Found supported GSM modem: {} (VID: {:?}, PID: {:?})",
                    device_info.path, device_info.vendor_id, device_info.product_id
                );
                gsm_devices.push(device_info);
            }
        }

        debug!("Found {} GSM devices", gsm_devices.len());
        Ok(gsm_devices)
    }
}
