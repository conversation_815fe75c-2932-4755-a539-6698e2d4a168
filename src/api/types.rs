use serde::{Deserialize, Serialize};

#[derive(Debug, Deserialize)]
pub struct SendSmsRequest {
    pub phone_number: String,
    pub message: String,
    pub device_key: Option<String>, // Optional: specify which device to use
}

#[derive(Debug, Serialize)]
pub struct SendSmsResponse {
    pub success: bool,
    pub message: String,
    pub device_used: Option<String>,
    pub response: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct DeviceStatusResponse {
    pub devices: Vec<DeviceInfo>,
    pub total_devices: usize,
}

#[derive(Debug, Serialize)]
pub struct DeviceInfo {
    pub device_key: String,
    pub device_path: String,
    pub sim_id: Option<String>,
    pub status: String,
}

#[derive(Debug, Serialize)]
pub struct HealthResponse {
    pub status: String,
    pub version: String,
    pub uptime_seconds: u64,
    pub devices_count: usize,
}
