use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use std::sync::<PERSON>;
use tracing::{info, error, warn};

use crate::gsm::service::GsmService;
use super::types::{SendSmsRequest, SendSmsResponse, DeviceStatusResponse, DeviceInfo, HealthResponse};

// Lock-free shared GSM service instance
pub type AppState = Arc<GsmService>;

/// Send SMS via any available device (lock-free shared instance)
pub async fn send_sms(
    State(gsm_service): State<AppState>,
    Json(request): Json<SendSmsRequest>,
) -> Result<Json<SendSmsResponse>, StatusCode> {
    // Validate request
    if request.phone_number.is_empty() {
        warn!("❌ API: Empty phone number provided");
        return Ok(Json(SendSmsResponse {
            success: false,
            message: "Phone number cannot be empty".to_string(),
            device_used: None,
            response: None,
        }));
    }

    if request.message.is_empty() {
        warn!("❌ API: Empty message provided");
        return Ok(Json(SendSmsResponse {
            success: false,
            message: "Message cannot be empty".to_string(),
            device_used: None,
            response: None,
        }));
    }

    info!("📤 API: Send SMS request to {} - {}", request.phone_number, request.message);

    // Use shared GSM service instance (lock-free via Arc)
    let result = if let Some(device_key) = &request.device_key {
        // Send via specific device
        gsm_service.send_sms_via_device(device_key, &request.phone_number, &request.message).await
    } else {
        // Send via any available device
        gsm_service.send_sms(&request.phone_number, &request.message).await
    };

    match result {
        Ok(response) => {
            info!("✅ API: SMS sent successfully");
            Ok(Json(SendSmsResponse {
                success: true,
                message: "SMS sent successfully".to_string(),
                device_used: request.device_key,
                response: Some(response),
            }))
        }
        Err(e) => {
            error!("❌ API: Failed to send SMS: {}", e);
            Ok(Json(SendSmsResponse {
                success: false,
                message: format!("Failed to send SMS: {}", e),
                device_used: None,
                response: None,
            }))
        }
    }
}

/// Send SMS via specific device (lock-free shared instance)
pub async fn send_sms_via_device(
    State(gsm_service): State<AppState>,
    Path(device_key): Path<String>,
    Json(request): Json<SendSmsRequest>,
) -> Result<Json<SendSmsResponse>, StatusCode> {
    // Validate request
    if request.phone_number.is_empty() {
        warn!("❌ API: Empty phone number provided");
        return Ok(Json(SendSmsResponse {
            success: false,
            message: "Phone number cannot be empty".to_string(),
            device_used: Some(device_key),
            response: None,
        }));
    }

    if request.message.is_empty() {
        warn!("❌ API: Empty message provided");
        return Ok(Json(SendSmsResponse {
            success: false,
            message: "Message cannot be empty".to_string(),
            device_used: Some(device_key),
            response: None,
        }));
    }

    info!("📤 API: Send SMS via device {} to {} - {}", device_key, request.phone_number, request.message);

    // Use shared GSM service instance (lock-free via Arc)
    match gsm_service.send_sms_via_device(&device_key, &request.phone_number, &request.message).await {
        Ok(response) => {
            info!("✅ API: SMS sent successfully via {}", device_key);
            Ok(Json(SendSmsResponse {
                success: true,
                message: "SMS sent successfully".to_string(),
                device_used: Some(device_key),
                response: Some(response),
            }))
        }
        Err(e) => {
            error!("❌ API: Failed to send SMS via {}: {}", device_key, e);
            Ok(Json(SendSmsResponse {
                success: false,
                message: format!("Failed to send SMS: {}", e),
                device_used: Some(device_key),
                response: None,
            }))
        }
    }
}

/// Get device status (lock-free shared instance)
pub async fn get_device_status(
    State(gsm_service): State<AppState>,
) -> Result<Json<DeviceStatusResponse>, StatusCode> {
    info!("📱 API: Get device status request");

    // Use shared GSM service instance to scan for devices (lock-free via Arc)
    match gsm_service.trigger_device_scan().await {
        Ok(device_keys) => {
            let devices: Vec<DeviceInfo> = device_keys.into_iter().map(|key| {
                DeviceInfo {
                    device_key: key.clone(),
                    device_path: format!("/dev/tty{}", key), // Simplified path
                    sim_id: None, // Would need to query actual SIM ID
                    status: "available".to_string(),
                }
            }).collect();

            info!("📱 API: Found {} devices", devices.len());
            Ok(Json(DeviceStatusResponse {
                total_devices: devices.len(),
                devices,
            }))
        }
        Err(e) => {
            warn!("⚠️  API: Device scan failed: {}", e);
            // Return empty list instead of error
            Ok(Json(DeviceStatusResponse {
                total_devices: 0,
                devices: vec![],
            }))
        }
    }
}

/// Health check endpoint
pub async fn health_check() -> Result<Json<HealthResponse>, StatusCode> {
    Ok(Json(HealthResponse {
        status: "healthy".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        uptime_seconds: 0, // Would need to track actual uptime
        devices_count: 1, // Would need to get from service
    }))
}
