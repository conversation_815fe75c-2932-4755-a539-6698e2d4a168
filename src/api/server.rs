use axum::{
    routing::{get, post},
    Router,
};
use std::sync::Arc;
use tower::ServiceBuilder;
use tower_http::{
    cors::CorsLayer,
    trace::TraceLayer,
};
use tracing::info;

use crate::gsm::service::GsmService;
use super::handlers::{send_sms, send_sms_via_device, get_device_status, health_check, AppState};

pub struct ApiServer {
    gsm_service: Arc<GsmService>,
    port: u16,
}

impl ApiServer {
    pub fn new(gsm_service: GsmService, port: u16) -> Self {
        Self {
            gsm_service: Arc::new(gsm_service),
            port,
        }
    }

    pub async fn start(self) -> anyhow::Result<()> {
        let app = self.create_router();
        let addr = format!("0.0.0.0:{}", self.port);
        
        info!("🚀 Starting SMS Worker API server on {} (lock-free shared instance)", addr);
        info!("📖 API Documentation:");
        info!("  POST /api/sms/send           - Send SMS via any device");
        info!("  POST /api/sms/send/:device   - Send SMS via specific device");
        info!("  GET  /api/devices            - Get device status");
        info!("  GET  /api/health             - Health check");

        let listener = tokio::net::TcpListener::bind(&addr).await?;
        axum::serve(listener, app).await?;
        
        Ok(())
    }

    fn create_router(&self) -> Router {
        Router::new()
            // SMS endpoints
            .route("/api/sms/send", post(send_sms))
            .route("/api/sms/send/:device", post(send_sms_via_device))
            
            // Device management
            .route("/api/devices", get(get_device_status))
            
            // Health check
            .route("/api/health", get(health_check))
            .route("/health", get(health_check))
            
            // Add middleware
            .layer(
                ServiceBuilder::new()
                    .layer(TraceLayer::new_for_http())
                    .layer(CorsLayer::permissive())
            )
            .with_state(self.gsm_service.clone())
    }
}
