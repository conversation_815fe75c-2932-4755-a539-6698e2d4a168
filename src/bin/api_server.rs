use anyhow::Result;
use clap::Parser;
use sms_worker::{
    config_types::AppConfig,
    gsm::service::GsmService,
    api::server::ApiServer,
};
use tracing::{info, error};

#[derive(Parser)]
#[command(name = "sms-api-server")]
#[command(about = "SMS Worker HTTP API Server")]
struct Cli {
    /// Port to run the API server on
    #[arg(short, long, default_value = "8080")]
    port: u16,
    
    /// Start only the API server without GSM service
    #[arg(long)]
    api_only: bool,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("info")
        .init();

    let cli = Cli::parse();

    // Load configuration
    let config = AppConfig::default();
    
    info!("🚀 Starting SMS Worker HTTP API Server");
    info!("🌐 API will be available on: http://localhost:{}", cli.port);
    info!("📱 GSM Configuration: {} baud, {}s timeout, {}s heartbeat", 
          config.gsm.baud_rate, 
          config.gsm.timeout_seconds, 
          config.gsm.heartbeat_interval_seconds);

    if cli.api_only {
        info!("⚠️  API-only mode: GSM service will not be started");
        info!("   SMS sending will fail until GSM devices are available");
    }

    // Create shared GSM service instance
    let gsm_service = GsmService::new(config).await?;

    if !cli.api_only {
        // Start GSM service in background for device management
        let mut gsm_service_bg = GsmService::new(AppConfig::default()).await?;
        tokio::spawn(async move {
            info!("📱 Starting background GSM service for device management...");
            if let Err(e) = gsm_service_bg.start().await {
                error!("Background GSM service error: {}", e);
            }
        });

        // Give GSM service time to initialize
        tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
    }

    // Create and start API server with shared GSM service (lock-free)
    let api_server = ApiServer::new(gsm_service, cli.port);
    
    info!("✅ Starting HTTP API server...");
    api_server.start().await?;
    
    Ok(())
}
