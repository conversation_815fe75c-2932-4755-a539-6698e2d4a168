use anyhow::Result;
use sms_worker::gsm::entity::GsmEntity;
use std::env;
use tokio::sync::mpsc;
use tracing::{info, error};

/// Simple CLI tool for sending SMS via GSM modem
/// Usage: cargo run --bin send_sms -- <device_path> <phone_number> <message>
/// Example: cargo run --bin send_sms -- /dev/ttyUSB0 "+1234567890" "Hello World"
#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("info")
        .init();

    let args: Vec<String> = env::args().collect();
    
    if args.len() != 4 {
        eprintln!("Usage: {} <device_path> <phone_number> <message>", args[0]);
        eprintln!("Example: {} /dev/ttyUSB0 \"+1234567890\" \"Hello World\"", args[0]);
        std::process::exit(1);
    }

    let device_path = &args[1];
    let phone_number = &args[2];
    let message = &args[3];

    info!("📱 SMS Sender CLI Tool");
    info!("🔌 Device: {}", device_path);
    info!("📞 To: {}", phone_number);
    info!("💬 Message: {}", message);

    // Create event channel (required for GsmEntity)
    let (event_sender, mut _event_receiver) = mpsc::unbounded_channel();

    // Create GSM entity
    let gsm_entity = GsmEntity::new(
        device_path.clone(),
        115200, // Standard baud rate
        30,     // 30 second timeout
        10,     // 10 second heartbeat
        event_sender,
    );

    info!("📤 Sending SMS...");
    
    match gsm_entity.send_sms_pdu(phone_number, message).await {
        Ok(response) => {
            info!("✅ SMS sent successfully!");
            info!("📥 Modem response: {}", response);
            println!("SUCCESS: SMS sent to {}", phone_number);
        }
        Err(e) => {
            error!("❌ Failed to send SMS: {}", e);
            eprintln!("ERROR: {}", e);
            std::process::exit(1);
        }
    }

    Ok(())
}
