[package]
name = "sms-worker"
version = "0.1.0"
edition = "2021"
description = "Rust implementation of SMS receiver service for GSM modems"
authors = ["SMS Receiver Team"]

[lib]
name = "sms_worker"
path = "src/lib.rs"

[dependencies]
tokio = { version = "1.47", features = ["full"] }
tokio-serial = "5.4"
#rdkafka = { version = "0.38"}
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
config = "0.15"
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
regex = "1.11"
clap = { version = "4.0", features = ["derive"] }

# HTTP API dependencies
axum = { version = "0.7", optional = true }
tower = { version = "0.4", optional = true }
tower-http = { version = "0.5", features = ["cors", "trace"], optional = true }

[features]
default = ["api"]
api = ["dep:axum", "dep:tower", "dep:tower-http"]