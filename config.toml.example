[kafka]
brokers = ["127.0.0.1:9092"]
client_id = "gsm"
group_id = "gsm-consumer"
topic = "CreateMessageDto"

[gsm]
# Auto-connection settings
scan_interval_seconds = 10  # Scan every 10 seconds for faster auto-connection (was 30)
baud_rate = 115200         # Standard GSM modem baud rate
timeout_seconds = 300      # Connection timeout

# For even faster auto-connection, you can set scan_interval_seconds to 5
# but this will use more CPU resources
